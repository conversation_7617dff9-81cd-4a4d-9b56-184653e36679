import { motion } from "framer-motion";
import React from "react";

export interface LinesProps {
  width?: number
  height?: number
  count?: number
}

export const Lines: React.FC<LinesProps> = ({
  width = 500,
  height = 200,
  count = 5
}) => {

  return (
    <svg
      width="100%"
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      preserveAspectRatio="xMidYMid meet"
    >
      <defs>
        <mask id="curveMask">
          {Array.from({ length: count }).map((_, i) => {
            // Position lines to start from center of badge width
            // Badges are positioned at these percentages with translateX(-50%) to center them
            const positions = [
              width * 0,      // 0% position, centered
              width * 0.25,   // 25% position, centered
              width * 0.5,    // 50% position, centered
              width * 0.75,   // 75% position, centered
              width * 1.0     // 100% position, centered
            ];
            const x = positions[i] || i * (width / (count - 1));
            // Create smooth curved lines that flow from each badge to center
            const centerX = width / 2;
            const centerY = height;

            // Control points for smooth curves
            const cp1X = x; // Start vertically from badge
            const cp1Y = height * 0.4; // First control point height
            const cp2X = centerX + (x - centerX) * 0.3; // Second control point curves toward center
            const cp2Y = height * 0.8; // Second control point height

            return (
              <path
                key={i}
                d={`M${x},0 C${cp1X},${cp1Y} ${cp2X},${cp2Y} ${centerX},${centerY}`}
                stroke="white"
                strokeWidth="2"
                fill="none"
              />
            );
          })}
        </mask>
        <linearGradient id="gradient" x1="0" x2="0" y1="0" y2="1">
          <motion.stop
            stopColor="rgba(0,0,0,0.5)"
            animate={{ offset: ["-150%", "100%"] }}
            transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
          />
          <motion.stop
            stopColor="transparent"
            animate={{ offset: ["-20%", "100%"] }}
            transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
          />
          <motion.stop
            stopColor="aquamarine"
            animate={{ offset: ["-12%", "108%"] }}
            transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
          />
          <motion.stop
            stopColor="rgba(0,0,0,0.5)"
            animate={{ offset: ["-8%", "112%"] }}
            transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
          />
        </linearGradient>
      </defs>
      <g mask="url(#curveMask)">
        <rect x="0" y="0" width={width} height={height} fill="#333" />
        <rect x="0" y="0" width={width} height={height} fill="url(#gradient)" />
      </g>
    </svg>
  )
};

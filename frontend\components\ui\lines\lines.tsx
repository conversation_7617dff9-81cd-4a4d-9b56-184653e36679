import { motion } from "framer-motion";
import React from "react";

export interface LinesProps {
  width?: number
  height?: number
  count?: number
}

export const Lines: React.FC<LinesProps> = ({
  width = 500,
  height = 200,
  count = 5
}) => {

  return (
    <svg
      width="100%"
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      preserveAspectRatio="xMidYMid meet"
    >
      <defs>
        <mask id="curveMask">
          {Array.from({ length: count }).map((_, i) => {
            // Position lines to start from center of badge width
            // Badges are positioned at these percentages with translateX(-50%) to center them
            const positions = [
              width * 0,      // 0% position, centered
              width * 0.25,   // 25% position, centered
              width * 0.5,    // 50% position, centered
              width * 0.75,   // 75% position, centered
              width * 1.0     // 100% position, centered
            ];
            const x = positions[i] || i * (width / (count - 1));
            // Create dramatic curved funnel lines matching reference image
            const centerX = width / 2;
            const centerY = height;

            // For dramatic curves, we need the lines to bow outward first, then curve sharply inward
            // This creates the beautiful funnel effect

            // First control point - creates outward bow
            const cp1X = x + (x - centerX) * 0.8; // Exaggerate outward movement
            const cp1Y = height * 0.3; // About 1/3 down

            // Second control point - creates sharp inward curve
            const cp2X = centerX + (x - centerX) * 0.2; // Close to center but not quite
            const cp2Y = height * 0.85; // Very close to bottom

            return (
              <path
                key={i}
                d={`M${x},0 C${cp1X},${cp1Y} ${cp2X},${cp2Y} ${centerX},${centerY}`}
                stroke="white"
                strokeWidth="2"
                fill="none"
              />
            );
          })}
        </mask>
        <linearGradient id="gradient" x1="0" x2="0" y1="0" y2="1">
          <motion.stop
            stopColor="rgba(0,0,0,0.5)"
            animate={{ offset: ["-150%", "100%"] }}
            transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
          />
          <motion.stop
            stopColor="transparent"
            animate={{ offset: ["-20%", "100%"] }}
            transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
          />
          <motion.stop
            stopColor="aquamarine"
            animate={{ offset: ["-12%", "108%"] }}
            transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
          />
          <motion.stop
            stopColor="rgba(0,0,0,0.5)"
            animate={{ offset: ["-8%", "112%"] }}
            transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
          />
        </linearGradient>
      </defs>
      <g mask="url(#curveMask)">
        <rect x="0" y="0" width={width} height={height} fill="#333" />
        <rect x="0" y="0" width={width} height={height} fill="url(#gradient)" />
      </g>
    </svg>
  )
};
